# 临时剪贴板 - <PERSON>flare Worker

一个基于 Cloudflare Workers 和 KV 存储的临时剪贴板和文件传输助手。

## 功能特性

- 📝 **文本存储**: 支持任意长度的文本内容存储
- 📁 **文件传输**: 支持任意格式文件的上传和下载
- 🔐 **密码保护**: 自定义密码保护数据安全
- ⏰ **自动过期**: 数据保存24小时后自动删除
- 🔥 **阅后即焚**: 取回数据后5分钟自动销毁
- 🌐 **全球加速**: 基于 Cloudflare 全球网络

## 界面预览

### 主界面
- 简洁的双按钮设计：存储和取回
- 清晰的功能说明和使用提示

### 存储界面
- 自定义密码输入框
- 文本内容输入区域
- 文件拖拽上传区域
- 实时文件信息显示

### 取回界面
- 密码输入验证
- 文本内容展示和复制
- 文件信息显示和下载

## 部署步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 创建 KV 命名空间

```bash
# 创建生产环境 KV 命名空间
wrangler kv:namespace create "TEMP_STORAGE"

# 创建预览环境 KV 命名空间
wrangler kv:namespace create "TEMP_STORAGE" --preview
```

### 3. 配置 wrangler.toml

将创建的 KV 命名空间 ID 填入 `wrangler.toml` 文件：

```toml
[[kv_namespaces]]
binding = "TEMP_STORAGE"
id = "your-production-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"
```

### 4. 本地开发

```bash
npm run dev
```

### 5. 部署到 Cloudflare

```bash
npm run deploy
```

## 技术实现

### 存储机制
- 使用 Cloudflare KV 存储数据
- 密码作为 KV 的键名
- 数据包含文本、文件（Base64编码）、时间戳等信息
- 设置 TTL 实现自动过期

### 安全特性
- 密码保护访问
- 数据自动过期
- 阅后即焚机制
- CORS 安全配置

### 文件处理
- 前端文件转换为 Base64 编码
- 后端存储编码后的文件数据
- 取回时重新构建文件并提供下载

## API 接口

### 存储数据
```
POST /api/store
Content-Type: multipart/form-data

参数:
- password: 自定义密码
- text: 文本内容（可选）
- file: 文件（可选）
```

### 取回数据
```
POST /api/retrieve
Content-Type: multipart/form-data

参数:
- password: 取件密码
```

## 使用限制

- 单个文件大小受 Cloudflare Workers 请求大小限制（100MB）
- KV 存储单个值大小限制为 25MB
- 数据保存时间最长24小时
- 取回后5分钟自动销毁

## 注意事项

1. 请妥善保管取件密码，丢失后无法找回数据
2. 敏感数据请谨慎使用，建议加密后再存储
3. 大文件传输可能需要较长时间，请耐心等待
4. 建议在稳定网络环境下使用

## 许可证

MIT License
