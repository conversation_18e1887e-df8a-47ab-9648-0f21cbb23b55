#!/bin/bash

echo "🚀 Cloudflare Worker 临时剪贴板部署脚本"
echo "========================================"

# 检查是否安装了 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查是否安装了 wrangler
if ! command -v wrangler &> /dev/null; then
    echo "📦 安装 Wrangler CLI..."
    npm install -g wrangler
fi

# 安装项目依赖
echo "📦 安装项目依赖..."
npm install

# 检查是否已登录 Cloudflare
echo "🔐 检查 Cloudflare 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "请先登录 Cloudflare:"
    wrangler login
fi

# 创建 KV 命名空间
echo "🗄️  创建 KV 命名空间..."
echo "正在创建生产环境 KV 命名空间..."
PROD_KV_ID=$(wrangler kv:namespace create "TEMP_STORAGE" --json | jq -r '.id')

echo "正在创建预览环境 KV 命名空间..."
PREVIEW_KV_ID=$(wrangler kv:namespace create "TEMP_STORAGE" --preview --json | jq -r '.id')

# 更新 wrangler.toml 配置
echo "⚙️  更新配置文件..."
sed -i.bak "s/your-kv-namespace-id/$PROD_KV_ID/g" wrangler.toml
sed -i.bak "s/your-preview-kv-namespace-id/$PREVIEW_KV_ID/g" wrangler.toml
rm wrangler.toml.bak

echo "✅ KV 命名空间创建完成:"
echo "   生产环境 ID: $PROD_KV_ID"
echo "   预览环境 ID: $PREVIEW_KV_ID"

# 部署到 Cloudflare
echo "🚀 部署到 Cloudflare Workers..."
wrangler deploy

echo ""
echo "🎉 部署完成！"
echo "你的临时剪贴板已经可以使用了。"
echo ""
echo "📝 使用说明:"
echo "1. 访问你的 Worker 域名"
echo "2. 点击'存储'按钮上传文本或文件"
echo "3. 设置自定义密码"
echo "4. 点击'取回'按钮并输入密码获取数据"
echo ""
echo "⚠️  注意事项:"
echo "- 数据保存24小时后自动删除"
echo "- 取回数据后5分钟自动销毁"
echo "- 请妥善保管取件密码"
