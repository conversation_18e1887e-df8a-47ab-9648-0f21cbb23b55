# 部署指南

## 前置要求

1. **Node.js 和 npm**
   - 下载并安装 Node.js: https://nodejs.org/
   - 安装完成后，npm 会自动安装

2. **Cloudflare 账户**
   - 注册 Cloudflare 账户: https://dash.cloudflare.com/sign-up
   - 确保账户已验证

## 快速部署（推荐）

运行自动部署脚本：

```bash
./deploy.sh
```

脚本会自动完成以下步骤：
- 安装 Wrangler CLI
- 安装项目依赖
- 创建 KV 命名空间
- 更新配置文件
- 部署到 Cloudflare

## 手动部署

### 1. 安装 Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare

```bash
wrangler login
```

### 3. 安装项目依赖

```bash
npm install
```

### 4. 创建 KV 命名空间

```bash
# 创建生产环境 KV 命名空间
wrangler kv:namespace create "TEMP_STORAGE"

# 创建预览环境 KV 命名空间
wrangler kv:namespace create "TEMP_STORAGE" --preview
```

### 5. 更新配置文件

将上一步获得的 KV 命名空间 ID 填入 `wrangler.toml`:

```toml
[[kv_namespaces]]
binding = "TEMP_STORAGE"
id = "your-production-kv-namespace-id"    # 替换为实际的生产环境 ID
preview_id = "your-preview-kv-namespace-id"  # 替换为实际的预览环境 ID
```

### 6. 本地测试（可选）

```bash
wrangler dev
```

### 7. 部署到生产环境

```bash
wrangler deploy
```

## 部署后配置

### 自定义域名（可选）

1. 在 Cloudflare Dashboard 中找到你的 Worker
2. 点击 "Triggers" 标签
3. 添加自定义域名

### 环境变量（可选）

如需添加环境变量，在 `wrangler.toml` 中添加：

```toml
[vars]
ENVIRONMENT = "production"
MAX_FILE_SIZE = "25000000"  # 25MB
```

## 故障排除

### 常见问题

1. **KV 命名空间创建失败**
   - 确保已正确登录 Cloudflare
   - 检查账户权限

2. **部署失败**
   - 检查 `wrangler.toml` 配置是否正确
   - 确保 KV 命名空间 ID 已正确填入

3. **访问 404 错误**
   - 等待几分钟让部署生效
   - 检查 Worker 域名是否正确

### 日志查看

```bash
# 查看实时日志
wrangler tail

# 查看部署状态
wrangler deployments list
```

## 更新部署

修改代码后重新部署：

```bash
wrangler deploy
```

## 删除部署

```bash
# 删除 Worker
wrangler delete

# 删除 KV 命名空间
wrangler kv:namespace delete --binding TEMP_STORAGE
```

## 监控和维护

1. **监控使用情况**
   - 在 Cloudflare Dashboard 中查看 Worker 分析
   - 监控 KV 存储使用量

2. **定期清理**
   - KV 数据会自动过期，无需手动清理
   - 可以通过 Wrangler 命令查看存储使用情况

3. **性能优化**
   - 监控响应时间
   - 根据使用情况调整缓存策略
