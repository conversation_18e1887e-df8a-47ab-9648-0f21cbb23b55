export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      switch (path) {
        case '/':
          return new Response(getMainHTML(), {
            headers: { 'Content-Type': 'text/html', ...corsHeaders }
          });
        
        case '/store':
          if (request.method === 'GET') {
            return new Response(getStoreHTML(), {
              headers: { 'Content-Type': 'text/html', ...corsHeaders }
            });
          } else if (request.method === 'POST') {
            return handleStore(request, env);
          }
          break;
        
        case '/retrieve':
          if (request.method === 'GET') {
            return new Response(getRetrieveHTML(), {
              headers: { 'Content-Type': 'text/html', ...corsHeaders }
            });
          } else if (request.method === 'POST') {
            return handleRetrieve(request, env);
          }
          break;
        
        case '/api/store':
          return handleStore(request, env);
        
        case '/api/retrieve':
          return handleRetrieve(request, env);
        
        default:
          return new Response('Not Found', { status: 404, headers: corsHeaders });
      }
    } catch (error) {
      console.error('Error:', error);
      return new Response('Internal Server Error', { 
        status: 500, 
        headers: corsHeaders 
      });
    }
  }
};

async function handleStore(request, env) {
  try {
    const formData = await request.formData();
    const password = formData.get('password');
    const text = formData.get('text');
    const file = formData.get('file');

    if (!password) {
      return new Response(JSON.stringify({ error: '密码不能为空' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = {
      text: text || '',
      file: null,
      fileName: null,
      fileType: null,
      createdAt: Date.now(),
      expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      accessed: false,
      accessedAt: null
    };

    // Handle file upload
    if (file && file.size > 0) {
      const fileBuffer = await file.arrayBuffer();
      const fileBase64 = btoa(String.fromCharCode(...new Uint8Array(fileBuffer)));
      
      data.file = fileBase64;
      data.fileName = file.name;
      data.fileType = file.type;
    }

    // Store in KV with password as key
    await env.TEMP_STORAGE.put(password, JSON.stringify(data), {
      expirationTtl: 24 * 60 * 60 // 24 hours in seconds
    });

    return new Response(JSON.stringify({ 
      success: true, 
      message: '数据已保存，24小时后自动删除' 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Store error:', error);
    return new Response(JSON.stringify({ error: '存储失败' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Retrieve error:', error);
    return new Response(JSON.stringify({ error: '获取失败' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

function getMainHTML() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时剪贴板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        h1 {
            color: #333;
            margin-bottom: 2rem;
            font-size: 2rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 3rem;
            font-size: 1rem;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            flex-direction: column;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .info {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 临时剪贴板</h1>
        <p class="subtitle">安全的临时文件和文本传输助手</p>

        <div class="button-group">
            <a href="/store" class="btn btn-primary">📤 存储</a>
            <a href="/retrieve" class="btn btn-secondary">📥 取回</a>
        </div>

        <div class="info">
            <p>• 所有数据仅保存24小时</p>
            <p>• 取回后5分钟自动销毁</p>
            <p>• 支持文本和文件传输</p>
        </div>
    </div>
</body>
</html>
  `;
}

function getStoreHTML() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储 - 临时剪贴板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        input[type="password"], input[type="text"], textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input[type="password"]:focus, input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .file-drop-zone {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .file-drop-zone:hover, .file-drop-zone.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-info {
            margin-top: 1rem;
            padding: 0.5rem;
            background: #e8f5e8;
            border-radius: 5px;
            font-size: 0.9rem;
            display: none;
        }

        .btn {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📤 存储数据</h1>

        <div id="message" class="message"></div>

        <form id="storeForm">
            <div class="form-group">
                <label for="password">自定义密码 *</label>
                <input type="password" id="password" name="password" required
                       placeholder="请输入取件密码">
            </div>

            <div class="form-group">
                <label for="text">文本信息</label>
                <textarea id="text" name="text"
                          placeholder="请输入要保存的文本信息（可选）"></textarea>
            </div>

            <div class="form-group">
                <label>文件上传</label>
                <div class="file-drop-zone" id="fileDropZone">
                    <p>📁 拖拽文件到此处或点击选择文件</p>
                    <p style="font-size: 0.8rem; color: #666; margin-top: 0.5rem;">支持任意格式文件</p>
                </div>
                <input type="file" id="fileInput" style="display: none;">
                <div id="fileInfo" class="file-info"></div>
            </div>

            <button type="submit" class="btn btn-primary" id="submitBtn">
                📤 确定存储
            </button>

            <a href="/" class="btn btn-secondary">🏠 返回首页</a>
        </form>
    </div>

    <script>
        const form = document.getElementById('storeForm');
        const fileDropZone = document.getElementById('fileDropZone');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const message = document.getElementById('message');
        const submitBtn = document.getElementById('submitBtn');

        let selectedFile = null;

        // File drop zone events
        fileDropZone.addEventListener('click', () => fileInput.click());

        fileDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropZone.classList.add('dragover');
        });

        fileDropZone.addEventListener('dragleave', () => {
            fileDropZone.classList.remove('dragover');
        });

        fileDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = \`
                <strong>已选择文件:</strong> \${file.name}<br>
                <strong>大小:</strong> \${formatFileSize(file.size)}<br>
                <strong>类型:</strong> \${file.type || '未知'}
            \`;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showMessage(text, type) {
            message.textContent = text;
            message.className = \`message \${type}\`;
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const password = document.getElementById('password').value;
            const text = document.getElementById('text').value;

            if (!password.trim()) {
                showMessage('请输入密码', 'error');
                return;
            }

            if (!text.trim() && !selectedFile) {
                showMessage('请输入文本或选择文件', 'error');
                return;
            }

            submitBtn.disabled = true;
            submitBtn.textContent = '存储中...';

            try {
                const formData = new FormData();
                formData.append('password', password);
                formData.append('text', text);
                if (selectedFile) {
                    formData.append('file', selectedFile);
                }

                const response = await fetch('/api/store', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    form.reset();
                    selectedFile = null;
                    fileInfo.style.display = 'none';
                } else {
                    showMessage(result.error || '存储失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请重试', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '📤 确定存储';
            }
        });
    </script>
</body>
</html>
  `;
}

function getRetrieveHTML() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>取回 - 临时剪贴板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        input[type="password"] {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input[type="password"]:focus {
            outline: none;
            border-color: #f5576c;
        }

        .btn {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result {
            margin-top: 2rem;
            display: none;
        }

        .result-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .result-section h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .text-content {
            background: white;
            padding: 1rem;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 200px;
            overflow-y: auto;
        }

        .file-download {
            background: white;
            padding: 1rem;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-info {
            flex: 1;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .download-btn:hover {
            background: #218838;
        }

        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .copy-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 取回数据</h1>

        <div id="message" class="message"></div>

        <form id="retrieveForm">
            <div class="form-group">
                <label for="password">取件密码</label>
                <input type="password" id="password" name="password" required
                       placeholder="请输入取件密码">
            </div>

            <button type="submit" class="btn btn-primary" id="submitBtn">
                📥 取回数据
            </button>

            <a href="/" class="btn btn-secondary">🏠 返回首页</a>
        </form>

        <div id="result" class="result">
            <div id="textSection" class="result-section" style="display: none;">
                <h3>📝 文本内容</h3>
                <div id="textContent" class="text-content"></div>
                <button class="copy-btn" onclick="copyText()">📋 复制文本</button>
            </div>

            <div id="fileSection" class="result-section" style="display: none;">
                <h3>📁 文件</h3>
                <div class="file-download">
                    <div class="file-info">
                        <div id="fileName"></div>
                        <div id="fileSize" style="font-size: 0.8rem; color: #666;"></div>
                    </div>
                    <button class="download-btn" onclick="downloadFile()">⬇️ 下载</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('retrieveForm');
        const message = document.getElementById('message');
        const submitBtn = document.getElementById('submitBtn');
        const result = document.getElementById('result');
        const textSection = document.getElementById('textSection');
        const fileSection = document.getElementById('fileSection');
        const textContent = document.getElementById('textContent');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        let retrievedData = null;

        function showMessage(text, type) {
            message.textContent = text;
            message.className = \`message \${type}\`;
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        function copyText() {
            navigator.clipboard.writeText(textContent.textContent).then(() => {
                showMessage('文本已复制到剪贴板', 'success');
            }).catch(() => {
                showMessage('复制失败', 'error');
            });
        }

        function downloadFile() {
            if (!retrievedData || !retrievedData.file) return;

            try {
                const byteCharacters = atob(retrievedData.file);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: retrievedData.fileType });

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = retrievedData.fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (error) {
                showMessage('文件下载失败', 'error');
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const password = document.getElementById('password').value;

            if (!password.trim()) {
                showMessage('请输入密码', 'error');
                return;
            }

            submitBtn.disabled = true;
            submitBtn.textContent = '取回中...';
            result.style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('password', password);

                const response = await fetch('/api/retrieve', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    retrievedData = data;
                    showMessage(data.message, 'success');

                    // Show text if available
                    if (data.text && data.text.trim()) {
                        textContent.textContent = data.text;
                        textSection.style.display = 'block';
                    } else {
                        textSection.style.display = 'none';
                    }

                    // Show file if available
                    if (data.file && data.fileName) {
                        fileName.textContent = data.fileName;
                        const fileSizeBytes = Math.round(data.file.length * 0.75); // Approximate size from base64
                        fileSize.textContent = formatFileSize(fileSizeBytes);
                        fileSection.style.display = 'block';
                    } else {
                        fileSection.style.display = 'none';
                    }

                    result.style.display = 'block';
                } else {
                    showMessage(data.error || '取回失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请重试', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '📥 取回数据';
            }
        });
    </script>
</body>
</html>
  `;
}

async function handleRetrieve(request, env) {
  try {
    const formData = await request.formData();
    const password = formData.get('password');

    if (!password) {
      return new Response(JSON.stringify({ error: '请输入密码' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const storedData = await env.TEMP_STORAGE.get(password);
    
    if (!storedData) {
      return new Response(JSON.stringify({ error: '密码错误或数据已过期' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = JSON.parse(storedData);
    
    // Check if data has expired
    if (Date.now() > data.expiresAt) {
      await env.TEMP_STORAGE.delete(password);
      return new Response(JSON.stringify({ error: '数据已过期' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Mark as accessed and set 5-minute deletion timer
    if (!data.accessed) {
      data.accessed = true;
      data.accessedAt = Date.now();
      
      // Update the data with accessed flag and set 5-minute expiration
      await env.TEMP_STORAGE.put(password, JSON.stringify(data), {
        expirationTtl: 5 * 60 // 5 minutes in seconds
      });
    }

    return new Response(JSON.stringify({
      success: true,
      text: data.text,
      file: data.file,
      fileName: data.fileName,
      fileType: data.fileType,
      message: '数据将在5分钟后自动删除'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Retrieve error:', error);
    return new Response(JSON.stringify({ error: '获取失败' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
