#!/usr/bin/env python3
"""
本地测试服务器 - 用于预览临时剪贴板界面
注意：这只是界面预览，不包含实际的KV存储功能
"""

import http.server
import socketserver
import json
import base64
import time
import os
from urllib.parse import parse_qs
from io import BytesIO

# 简单的内存存储（仅用于本地测试）
memory_storage = {}

class LocalHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_main_page()
        elif self.path == '/store':
            self.send_store_page()
        elif self.path == '/retrieve':
            self.send_retrieve_page()
        else:
            self.send_error(404)
    
    def do_POST(self):
        if self.path == '/api/store':
            self.handle_store()
        elif self.path == '/api/retrieve':
            self.handle_retrieve()
        else:
            self.send_error(404)
    
    def send_main_page(self):
        html = self.get_main_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_store_page(self):
        html = self.get_store_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_retrieve_page(self):
        html = self.get_retrieve_html()
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def handle_store(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            # 简单解析（实际应该用proper multipart parser）
            data_str = post_data.decode('utf-8', errors='ignore')
            
            # 提取密码（简化版本）
            password = "test123"  # 默认密码用于演示
            
            # 存储数据
            memory_storage[password] = {
                'text': '这是一个本地测试数据',
                'file': None,
                'fileName': None,
                'fileType': None,
                'createdAt': time.time() * 1000,
                'expiresAt': time.time() * 1000 + 24 * 60 * 60 * 1000,
                'accessed': False,
                'accessedAt': None
            }
            
            response = {'success': True, 'message': '数据已保存（本地测试模式）'}
            self.send_json_response(response)
            
        except Exception as e:
            response = {'error': f'存储失败: {str(e)}'}
            self.send_json_response(response, 500)
    
    def handle_retrieve(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            # 简化版本，直接返回测试数据
            password = "test123"
            
            if password in memory_storage:
                data = memory_storage[password]
                response = {
                    'success': True,
                    'text': data['text'],
                    'file': data['file'],
                    'fileName': data['fileName'],
                    'fileType': data['fileType'],
                    'message': '数据获取成功（本地测试模式）'
                }
            else:
                response = {'error': '密码错误或数据不存在（请先存储数据）'}
                self.send_json_response(response, 404)
                return
            
            self.send_json_response(response)
            
        except Exception as e:
            response = {'error': f'获取失败: {str(e)}'}
            self.send_json_response(response, 500)
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def get_main_html(self):
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时剪贴板 - 本地预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 3rem;
            font-size: 1rem;
        }
        
        .button-group {
            display: flex;
            gap: 1rem;
            flex-direction: column;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .info {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .local-notice {
            margin-top: 1rem;
            padding: 1rem;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 临时剪贴板</h1>
        <p class="subtitle">安全的临时文件和文本传输助手</p>
        
        <div class="button-group">
            <a href="/store" class="btn btn-primary">📤 存储</a>
            <a href="/retrieve" class="btn btn-secondary">📥 取回</a>
        </div>
        
        <div class="info">
            <p>• 所有数据仅保存24小时</p>
            <p>• 取回后5分钟自动销毁</p>
            <p>• 支持文本和文件传输</p>
        </div>
        
        <div class="local-notice">
            <strong>🔧 本地预览模式</strong><br>
            这是界面预览版本，实际功能需要部署到 Cloudflare Workers
        </div>
    </div>
</body>
</html>
        """

    def get_store_html(self):
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储 - 临时剪贴板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        h1 { color: #333; margin-bottom: 2rem; text-align: center; font-size: 1.8rem; }
        .form-group { margin-bottom: 1.5rem; }
        label { display: block; margin-bottom: 0.5rem; color: #333; font-weight: 600; }
        input[type="password"], textarea {
            width: 100%; padding: 0.8rem; border: 2px solid #e1e5e9;
            border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease;
        }
        input[type="password"]:focus, textarea:focus {
            outline: none; border-color: #667eea;
        }
        textarea { resize: vertical; min-height: 100px; }
        .file-drop-zone {
            border: 2px dashed #ccc; border-radius: 10px; padding: 2rem;
            text-align: center; cursor: pointer; transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .file-drop-zone:hover { border-color: #667eea; background: #f0f4ff; }
        .btn {
            width: 100%; padding: 1rem; border: none; border-radius: 10px;
            font-size: 1.1rem; font-weight: 600; cursor: pointer;
            transition: all 0.3s ease; margin-bottom: 1rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0,0,0,0.2); }
        .message {
            padding: 1rem; border-radius: 10px; margin-bottom: 1rem; display: none;
        }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .local-notice {
            margin-bottom: 1rem; padding: 1rem; background: #fff3cd;
            border: 1px solid #ffeaa7; border-radius: 10px;
            font-size: 0.9rem; color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📤 存储数据</h1>

        <div class="local-notice">
            <strong>🔧 本地预览模式</strong><br>
            界面功能演示，实际存储需要部署到 Cloudflare Workers
        </div>

        <div id="message" class="message"></div>

        <form id="storeForm">
            <div class="form-group">
                <label for="password">自定义密码 *</label>
                <input type="password" id="password" name="password" required
                       placeholder="请输入取件密码">
            </div>

            <div class="form-group">
                <label for="text">文本信息</label>
                <textarea id="text" name="text"
                          placeholder="请输入要保存的文本信息（可选）"></textarea>
            </div>

            <div class="form-group">
                <label>文件上传</label>
                <div class="file-drop-zone" id="fileDropZone">
                    <p>📁 拖拽文件到此处或点击选择文件</p>
                    <p style="font-size: 0.8rem; color: #666; margin-top: 0.5rem;">支持任意格式文件</p>
                </div>
                <input type="file" id="fileInput" style="display: none;">
            </div>

            <button type="submit" class="btn btn-primary" id="submitBtn">
                📤 确定存储
            </button>

            <a href="/" class="btn btn-secondary">🏠 返回首页</a>
        </form>
    </div>

    <script>
        const form = document.getElementById('storeForm');
        const message = document.getElementById('message');
        const submitBtn = document.getElementById('submitBtn');

        function showMessage(text, type) {
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            setTimeout(() => { message.style.display = 'none'; }, 5000);
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            showMessage('本地预览模式：数据已模拟存储', 'success');
        });
    </script>
</body>
</html>
        """

    def get_retrieve_html(self):
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>取回 - 临时剪贴板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        .container {
            background: white; padding: 2rem; border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; width: 100%;
        }
        h1 { color: #333; margin-bottom: 2rem; text-align: center; font-size: 1.8rem; }
        .form-group { margin-bottom: 1.5rem; }
        label { display: block; margin-bottom: 0.5rem; color: #333; font-weight: 600; }
        input[type="password"] {
            width: 100%; padding: 0.8rem; border: 2px solid #e1e5e9;
            border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease;
        }
        input[type="password"]:focus { outline: none; border-color: #f5576c; }
        .btn {
            width: 100%; padding: 1rem; border: none; border-radius: 10px;
            font-size: 1.1rem; font-weight: 600; cursor: pointer;
            transition: all 0.3s ease; margin-bottom: 1rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0,0,0,0.2); }
        .message {
            padding: 1rem; border-radius: 10px; margin-bottom: 1rem; display: none;
        }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .local-notice {
            margin-bottom: 1rem; padding: 1rem; background: #fff3cd;
            border: 1px solid #ffeaa7; border-radius: 10px;
            font-size: 0.9rem; color: #856404;
        }
        .result {
            margin-top: 2rem; display: none;
        }
        .result-section {
            background: #f8f9fa; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;
        }
        .text-content {
            background: white; padding: 1rem; border-radius: 5px;
            border: 1px solid #dee2e6; white-space: pre-wrap; word-break: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 取回数据</h1>

        <div class="local-notice">
            <strong>🔧 本地预览模式</strong><br>
            界面功能演示，输入任意密码查看效果
        </div>

        <div id="message" class="message"></div>

        <form id="retrieveForm">
            <div class="form-group">
                <label for="password">取件密码</label>
                <input type="password" id="password" name="password" required
                       placeholder="请输入取件密码">
            </div>

            <button type="submit" class="btn btn-primary" id="submitBtn">
                📥 取回数据
            </button>

            <a href="/" class="btn btn-secondary">🏠 返回首页</a>
        </form>

        <div id="result" class="result">
            <div class="result-section">
                <h3>📝 文本内容</h3>
                <div class="text-content">这是一个本地预览示例文本内容。在实际部署后，这里会显示真实的存储数据。</div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('retrieveForm');
        const message = document.getElementById('message');
        const result = document.getElementById('result');

        function showMessage(text, type) {
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            setTimeout(() => { message.style.display = 'none'; }, 5000);
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            showMessage('本地预览模式：模拟数据获取成功', 'success');
            result.style.display = 'block';
        });
    </script>
</body>
</html>
        """


if __name__ == '__main__':
    PORT = 8000
    print(f"🚀 启动本地预览服务器...")
    print(f"📱 访问地址: http://localhost:{PORT}")
    print(f"💡 这是界面预览版本，实际功能需要部署到 Cloudflare Workers")
    print(f"🔧 按 Ctrl+C 停止服务器")
    print("-" * 50)

    with socketserver.TCPServer(("", PORT), LocalHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
            httpd.shutdown()
